package com.crrc.network.api;


import static com.crrc.common.Constant.MOBILE_TERMINAL;
import static com.crrc.common.Constant.RULE;

import io.reactivex.Observable;

import com.crrc.common.bean.response.AddressBookListResponse;
import com.crrc.common.bean.response.AlarmResponse;
import com.crrc.common.bean.response.Article;


import com.crrc.common.BaseResponse;

import java.util.List;
import java.util.Map;

import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;
import retrofit2.http.Query;
import retrofit2.http.Streaming;
import retrofit2.http.Url;

import com.crrc.common.bean.response.CaseListResponse;
import com.crrc.common.bean.response.ChatInfo;
import com.crrc.common.bean.response.CurrentDirsResponse;
import com.crrc.common.bean.response.DeviceDetailResponse;
import com.crrc.common.bean.response.DeviceInfo;
import com.crrc.common.bean.response.DirsListResponse;
import com.crrc.common.bean.response.EmergencyDetailResponse;
import com.crrc.common.bean.response.EmergencyEventListResponse;
import com.crrc.common.bean.response.FaultDetailResponse;
import com.crrc.common.bean.response.FilesByLabelResponse;
import com.crrc.common.bean.response.GroupMemberInfoResponse;
import com.crrc.common.bean.response.LabelListResponse;
import com.crrc.common.bean.response.LineDevice;
import com.crrc.common.bean.response.LineTool;
import com.crrc.common.bean.response.LoginBean;
import com.crrc.common.bean.response.MembersResponse;
import com.crrc.common.bean.response.OrderFilterParamResponse;
import com.crrc.common.bean.response.SearchFileResponse;
import com.crrc.common.bean.response.TaskOverviewResponse;
import com.crrc.common.bean.response.LineInfoResponse;
import com.crrc.common.bean.response.StationAlarmResponse;
import com.crrc.common.bean.response.SearchResponse;
import com.crrc.common.bean.response.PlanOrderListResponse;
import com.crrc.common.bean.response.FaultOrderListResponse;
import com.crrc.common.bean.response.EmergencyOrderListResponse;
import com.crrc.common.bean.response.TempOrderListResponse;
import com.crrc.common.bean.response.TempWorkOrderParamResponse;
import com.crrc.common.bean.response.UserInfoResponse;
import com.crrc.common.bean.response.WorkOrderCountResponse;
import com.crrc.common.bean.response.WorkOrderDetailResponse;
import com.crrc.common.bean.response.PickMaterialResponse;
import com.crrc.common.bean.response.CaseDetailResponse;
import com.crrc.common.bean.response.ProcedureResponse;
import com.crrc.common.bean.response.WorkorderStatics;

public interface ApiInterface {


    // 测试

    /**
     * 下载文件
     *
     * @param fileUrl 文件url
     * @return a
     */
    @Streaming
    @GET
    Observable<ResponseBody> downLoad(@Url String fileUrl);


    @GET(RULE + "/?apipost_id=1672ffee363006")
    Observable<BaseResponse<List<Article>>> getArticles(@Query("page") int page, @Query("pageSize") int pageSize);

    //#######################首页接口######################//

    /**
     * 获取任务总览信息
     *
     * @return 任务总览数据
     */
    @POST(MOBILE_TERMINAL + "/home/<USER>")
    Observable<BaseResponse<TaskOverviewResponse>> getTaskOverview();

    /**
     * 搜索
     *
     * @param  keyword
     * @return 搜索结果
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/home/<USER>")
    Observable<BaseResponse<SearchResponse>> search(@Field("keyword") String keyword);

    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/home/<USER>")
    Observable<BaseResponse<DeviceDetailResponse>> scan(@Field("data") String data,
                                                        @Field("type") String type);

    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/home/<USER>")
    Observable<BaseResponse<DeviceDetailResponse>> getDeviceDetailById(@Field("id") String id);
    //#######################线路图接口######################//

    /**
     * 线路统计信息
     *
     * @return 线路统计信息
     */
    @POST(MOBILE_TERMINAL + "/line/statistics")
    Observable<BaseResponse<List<LineInfoResponse>>> getLineInfo();

    /**
     * 线路报警统计信息
     *
     * @param stationId
     * @return
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/statistics/alarm")
    Observable<BaseResponse<List<StationAlarmResponse>>> getStationAlarmInfo(@Field("stationId") String stationId);

    /**
     * 备品备件信息
     *
     * @param  storeId
     * @return 备品备件信息
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/line/statistics/device")
    Observable<BaseResponse<List<LineDevice>>> getLineDeviceInfo(@Field("storeId") String storeId);

    /**
     * 工器具信息
     *
     * @param  storeId
     * @return
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/line/statistics/tools")
    Observable<BaseResponse<List<LineTool>>> getLineToolsInfo(@Field("storeId") String storeId);


    //#######################工单接口######################//

    /**
     * 工单筛选参数
     *
     * @return 筛选到的工单列表参数
     */
    @POST(MOBILE_TERMINAL + "/order/opt")
    Observable<BaseResponse<OrderFilterParamResponse>> getFilterParamList();

//    @POST
//    Observable<BaseResponse<OrderFilterParamResponse>> getFilterParamList(@Url String fullUrl);

    /**
     * 获取计划工单列表
     *
     * @return 计划工单列表数据
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/list/plan")
    Observable<BaseResponse<PlanOrderListResponse>> getPlanOrderList(@Field("pageNum") int pageNum,
                                                                     @Field("pageSize") int pageSize,
                                                                     @Field("orderOpt") String orderOpt);
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/list/fault")
    Observable<BaseResponse<FaultOrderListResponse>> getFaultOrderList(@Field("pageNum") int pageNum,
                                                                       @Field("pageSize") int pageSize,
                                                                       @Field("orderOpt") String orderOpt);

    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/list/emergency")
    Observable<BaseResponse<EmergencyOrderListResponse>> getEmergencyOrderList(@Field("pageNum") int pageNum,
                                                                               @Field("pageSize") int pageSize,
                                                                               @Field("orderOpt") String orderOpt);

    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/list/temp")
    Observable<BaseResponse<TempOrderListResponse>> getTempOrderList(@Field("pageNum") int pageNum,
                                                                     @Field("pageSize") int pageSize,
                                                                     @Field("orderOpt") String orderOpt);

    /**
     * 获取工单数量统计
     *
     * @return 工单数量统计数据
     */
    @POST(MOBILE_TERMINAL + "/order/list/view")
    Observable<BaseResponse<WorkOrderCountResponse>> getWorkOrderCount();

    /**
     * 获取工单详情
     *
     * @return 工单详情数据
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/detail")
    Observable<BaseResponse<WorkOrderDetailResponse>> getWorkOrderDetail(@Field("id") String id);

    /**
     * 获取领料清单
     *
     * @param id 工单ID
     * @return 领料清单数据
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/pick/list")
    Observable<BaseResponse<PickMaterialResponse>> getPickMaterialList(@Field("id") String id);

    /**
     * 提交领料
     * @param id
     * @param orderPick
     * @return
     */

    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/pick")
    Observable<BaseResponse<Object>> submitPickMaterialList(@Field("id") String id,@Field("orderPick") String orderPick);

    /**
     * 工单回退
     *
     * @param id 工单ID
     * @return 回退结果
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/rollback")
    Observable<BaseResponse<Object>> rollbackWorkOrder(@Field("id") String id);

    /**
     * 工单作废
     *
     * @param id 工单ID
     * @return 作废结果
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/cancel")
    Observable<BaseResponse<Object>> cancelWorkOrder(@Field("id") String id);

    /**
     * 工单接单
     *
     * @param id 工单ID
     * @return 接单结果
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/accept")
    Observable<BaseResponse<Object>> acceptWorkOrder(@Field("id") String id);

    /**
     * 工单到场
     *
     * @param id 工单ID
     * @return 到场结果
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/arrive")
    Observable<BaseResponse<Object>> arriveWorkOrder(@Field("id") String id);

    /**
     * 工单处理
     *
     * @param id 工单ID
     * @return 处理结果
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/deal")
    Observable<BaseResponse<Object>> dealWorkOrder(@Field("id") String id);

    /**
     * 工单确认
     *
     * @param id 工单ID
     * @return 确认结果
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/confirm")
    Observable<BaseResponse<Object>> confirmWorkOrder(@Field("id") String id);


    /**
     * 获取创建临时工单的参数
     * @return  TempWorkOrderParamResponse
     */
    @POST(MOBILE_TERMINAL + "/order/build/opt")
    Observable<BaseResponse<TempWorkOrderParamResponse>> getTempWorkOrderParam();

    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/build/opt")
    Observable<BaseResponse<Object>> submitTempWorkOrderParam(@Field("orderBuildParam") String param);



    /**
     * 故障处置-维修规程
     *
     * @param id 工单ID
     * @return
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/repair/procedure")
    Observable<BaseResponse<ProcedureResponse>> procedure(@Field("id") String id);

    /**
     * 故障处置-维修确认
     *
     * @param   procedures
     * @return
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/repair/confirm")
    Observable<BaseResponse<Object>> procedureConfirm(@Field(value = "procedure",encoded = true) String procedures);

    /**
     * 故障处置-通过
     *
     * @param  id，procedureId
     * @return
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/repair/procedure/status/pass")
    Observable<BaseResponse<Object>> procedurePass(@Field("id") String id,
                                                   @Field("procedureId") String procedureId);

    /**
     * 故障处置-通过
     *
     * @param  id，procedureId
     * @return
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/repair/procedure/status/fail")
    Observable<BaseResponse<Object>> procedureFail(@Field("id") String id,
                                                   @Field("procedureId") String procedureId);

    //#######################案例库接口######################

    /**
     * 首页获取案例库列表
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 案例库列表数据
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/case/list")
    Observable<BaseResponse<CaseListResponse>> getCaseList(
            @Field("pageNum") int pageNum,
            @Field("pageSize") int pageSize
    );

    /**
     * 故障处置获取案例库列表
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 案例库列表数据
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/case/faultList")
    Observable<BaseResponse<CaseListResponse>> getCaseFaultList(
            @Field("id") String id,
            @Field("pageNum") int pageNum,
            @Field("pageSize") int pageSize
    );

    /**
     * 获取案例详情
     *
     * @param id 案例ID
     * @return 案例详情数据
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/case/detail")
    Observable<BaseResponse<CaseDetailResponse>> getCaseDetail(@Field("id") String id);

    /**
     * 工单指派人员获取
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/assign/opt")
    Observable<BaseResponse<MembersResponse>> assignOpt(@Field("id") String id);

    /**
     * 工单人员指派提交
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/order/assign")
    Observable<BaseResponse<Object>> assign(@Field(value = "id") String id,@Field(value = "orderAssignParam") String orderAssignParam);
//    ############################故障上报接口#############################

    /**
     * 获取设备信息  用于故障提报
     *
     * @return
     */
    @POST(MOBILE_TERMINAL + "/home/<USER>")
    Observable<BaseResponse<Map<String, Map<String, List<DeviceInfo>>>>> device();

    /**
     * 获取报警现象
     *
     * @param type
     * @return
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/home/<USER>/info")
    Observable<BaseResponse<List<AlarmResponse>>> alarmInfo(@Field("type") int type);

    /**
     * 获取故障详情
     *
     * @param alarmId
     * @return
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/home/<USER>/detail")
    Observable<BaseResponse<FaultDetailResponse>> faultDetail(@Field("alarmId") String alarmId);

    /**
     * 提交故障
     *
     * @param
     * @return
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/home/<USER>/report")
    Observable<BaseResponse<Object>> submitFault(@Field("deviceId") int deviceId,
                                                 @Field("alarmId") String alarmId,
                                                 @Field("faultDescription") String faultDescription,
                                                 @Field("repairSuggestion") String repairSuggestion);
//    ############################应急检测接口#############################

    /**
     * 获取应急检测列表
     */
    @POST(MOBILE_TERMINAL + "/emergency/list")
    Observable<BaseResponse<EmergencyEventListResponse>> getEmergencyEventList();

    /**
     * 获取应急详情
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/emergency/detail")
    Observable<BaseResponse<EmergencyDetailResponse>> getEmergencyEventStatus(@Field("uuid") String uuid);

    //    ############################用户信息接口#############################

    /**
     * 获取用户信息
     *
     * @param userId
     * @return
     */
    @FormUrlEncoded
    @POST(RULE + "/mobileTerminal/user/userById")
    Observable<BaseResponse<UserInfoResponse>> getUserById(@Field("userId") String userId);


    /**
     * 获取用户信息
     *
     * @param groupId
     * @return
     */
    @FormUrlEncoded
    @POST(RULE + "/mobileTerminal/user/group/members")
    Observable<BaseResponse<List<GroupMemberInfoResponse>>> getGroupMemberInfoById(@Field("groupId") String groupId);

    /**
     * 上传用户头像
     *
     * @param body files(图像)+userId
     * @return
     */
    @Multipart
    @POST(RULE + "/config/user/loadUserAvatar")
    Observable<BaseResponse<Object>> loadUserAvatar(@Part MultipartBody.Part files, @Part("userId") RequestBody body);

    /**
     * 下载用户头像
     *
     * @param
     * @return
     */
    @FormUrlEncoded
    @POST(RULE + "/config/user/getUserAvatar")
    Observable<BaseResponse<Object>> getUserAvatar(@Field("userId") String userId);

    /**
     * 登录
     *
     * @param username+ password
     * @return
     */
    @FormUrlEncoded
    @POST(RULE + "/config/user/login")
    Observable<BaseResponse<LoginBean>> login(
            @Field("username") String username,
            @Field("password") String password
    );

    /**
     * 退出登录
     *
     * @param
     * @return
     */
    @POST(RULE + "/config/user/logout")
    Observable<BaseResponse<Object>> logout();

    /**
     * 用户完成信息统计
     * @return
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/user/workOrderStatistics")
    Observable<BaseResponse<WorkorderStatics>> getWorkOrderStatistics(@Field("time") String time,
                                                                      @Field("timeType") int timeType,
                                                                      @Field("userId") String userId);

    //    ############################文档管理#############################

    /**
     * 获取当前文件路径
     * @param id 当前文件夹的id
     * @return
     */
    @FormUrlEncoded
    @POST(RULE + "/config/file/getCurrentDirs")
    Observable<BaseResponse<CurrentDirsResponse>> getCurrentDirs(@Field("id") String id);

    /**
     * 获取当前文件夹的文件列表
     * @param dirIds 文件id路径
     * @return
     */
    @FormUrlEncoded
    @POST(RULE + "/config/file/list")
    Observable<BaseResponse<DirsListResponse>> getDirsList(@Field("dirIds") String dirIds);
    /**
     * 获取文件标签列表
     * @param
     * @return
     */
    @POST(RULE + "/config/file/getLabelList")
    Observable<BaseResponse<List<LabelListResponse>>> getLabelList();

    /**
     * 根据标签获取文件
     * @param labelId 标签id
     * @return
     */
    @FormUrlEncoded
    @POST(RULE + "/config/file/getFilesByLabel")
    Observable<BaseResponse<FilesByLabelResponse>> getFilesByLabel(@Field("labelId") String labelId);
    /**
     * 模糊查询文件名
     * @param fileName 文件名
     * @return
     */
    @FormUrlEncoded
    @POST(RULE + "/config/file/searchFileName")
    Observable<BaseResponse<SearchFileResponse>> searchFileName(@Field("fileName") String fileName);

    /**
     * 文件下载
     * @param id 文件id
     * @return
     */
    @FormUrlEncoded
    @POST(RULE + "/config/file/downLoad")
    Observable<BaseResponse<Object>> downLoadFile(@Field("id") String id);

    //####################聊天接口#########################
    /**
     * 获取会话
     * @param
     * /获取会话的id，在用户点击群或者用户的时候调用，知道该往什么会话中发送信息
     * 但是现在没有建群的功能，群聊的会话id就是群组信息的id，type目前只有single，就是单聊
     * @return
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/chat/conversation")
    Observable<BaseResponse<String>> getConversation(@Field("type") int type,
                                                  @Field("initiatorUserId") String initiatorUserId,
                                                  @Field("targetId") String targetId);

    /**
     * 获取通讯录
     */
    @FormUrlEncoded
    @POST(MOBILE_TERMINAL + "/user/addressBook/list")
    Observable<BaseResponse<AddressBookListResponse>> getAddressBookList(@Field("userId") String userId);

    /**
     * 获取用户的聊天参与列表，按照是否有未读和未读时间排序
     * @return
     */
    @POST(MOBILE_TERMINAL + "/mobileTerminal/participants")
    Observable<BaseResponse<List<ChatInfo>>> getChatList(@Field("userId") String userId);


}
