package com.crrc.siom.ui.workorder.pages.detail

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Download
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Phone
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.crrc.common.bean.response.MembersResponse
import com.crrc.common.bean.response.WorkOrderDetailResponse
import com.crrc.siom.ui.components.MultiSelectDialog
import com.crrc.siom.ui.components.SingleSelectDialog
import com.crrc.siom.ui.workorder.PickMaterialActivity
import com.crrc.siom.ui.workorder.PickMaterialActivity.Companion.EXTRA_WORK_ORDER_ID
import com.crrc.siom.ui.workorder.ProcessWorkOrderActivity
import com.crrc.siom.ui.workorder.components.HandleWorkOrderActionResult
import com.crrc.siom.ui.workorder.components.WorkOrderAction
import com.crrc.siom.ui.workorder.components.WorkOrderActionDialog
import com.crrc.siom.ui.workorder.components.WorkOrderActionDialogState
import com.crrc.siom.ui.workorder.components.rememberWorkOrderActionDialogState
import com.crrc.siom.ui.workorder.viewmodel.WorkOrderDetailViewModel
import com.crrc.siom.utils.DownloadUtil

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WorkOrderDetailPage(
    workOrderId: String,
    onBackClick: () -> Unit,
    viewModel: WorkOrderDetailViewModel = viewModel()
) {
    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            viewModel.loadWorkOrderDetail(workOrderId)
        }
    }

    val context = LocalContext.current
    val workOrderDetail by viewModel.workOrderDetail.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()

    // 工单操作相关状态
    val isAcceptLoading by viewModel.isAcceptLoading.collectAsState(false)
    val isDealLoading by viewModel.isDealLoading.collectAsState(false)
    val isConfirmLoading by viewModel.isConfirmLoading.collectAsState(false)
    val isArriveLoading by viewModel.isArriveLoading.collectAsState(false)
    val isRollbackLoading by viewModel.isRollbackLoading.collectAsState(false)
    val isCancelLoading by viewModel.isCancelLoading.collectAsState(false)

    val acceptResult by viewModel.acceptResult.collectAsState(null)
    val dealResult by viewModel.dealResult.collectAsState(null)
    val confirmResult by viewModel.confirmResult.collectAsState(null)
    val arriveResult by viewModel.arriveResult.collectAsState(null)
    val rollbackResult by viewModel.rollbackResult.collectAsState(null)
    val cancelResult by viewModel.cancelResult.collectAsState(null)

    // 创建对话框状态
    val acceptDialogState = rememberWorkOrderActionDialogState()
    val dealDialogState = rememberWorkOrderActionDialogState()
    val confirmDialogState = rememberWorkOrderActionDialogState()
    val arriveDialogState = rememberWorkOrderActionDialogState()
    val rollbackDialogState = rememberWorkOrderActionDialogState()
    val cancelDialogState = rememberWorkOrderActionDialogState()

    // 指派相关状态
    var showAssignDialog by remember { mutableStateOf(false) }
    var membersResponse by remember { mutableStateOf<MembersResponse?>(null) }
    var selectedWorkOrderId by remember { mutableStateOf("") }
    var selectedMember by remember { mutableStateOf<MembersResponse.FilterParam?>(null) }


    // 处理指派
    fun handleAssign(workOrderId: String) {
        viewModel.assignOpt(workOrderId) { response, error ->
            if (response != null) {
                membersResponse = response
                showAssignDialog = true
            } else {
                Toast.makeText(context, "获取可指派人员失败: $error", Toast.LENGTH_SHORT).show()
            }
        }
    }

    // 提交指派
    fun submitAssignment(workOrderId: String) {
        val allMembers = membersResponse?.members?.map { member ->
            val modifiedMember = MembersResponse.FilterParam()
            modifiedMember.id = member.id
            modifiedMember.name = member.name
            modifiedMember.isSelected = (member.id == selectedMember?.id)
            modifiedMember
        }
        viewModel.assign(workOrderId, allMembers ?: emptyList()) { success, error ->
            if (success) {
                Toast.makeText(context, "指派成功", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(context, "指派失败: $error", Toast.LENGTH_SHORT).show()
            }
            showAssignDialog = false
        }
    }

    // 指派对话框
    if (showAssignDialog && membersResponse != null) {
        SingleSelectDialog(
            title = "选择指派人员",
            items = membersResponse?.members ?: emptyList(),
            selectedItem = selectedMember,
            itemText = { member -> member.name },
            onDismiss = { showAssignDialog = false },
            onConfirm = { selected ->
                selectedMember = selected
                submitAssignment(selectedWorkOrderId)
            }
        )
    }

    // 处理工单操作结果
    HandleWorkOrderActionResult(
        result = acceptResult,
        action = WorkOrderAction.Accept,
        onSuccess = { viewModel.loadWorkOrderDetail(workOrderId) },
        onResetResult = { viewModel.resetAcceptResult() }
    )

    HandleWorkOrderActionResult(
        result = dealResult,
        action = WorkOrderAction.Deal,
        onSuccess = { viewModel.loadWorkOrderDetail(workOrderId) },
        onResetResult = { viewModel.resetDealResult() }
    )

    HandleWorkOrderActionResult(
        result = confirmResult,
        action = WorkOrderAction.Confirm,
        onSuccess = { viewModel.loadWorkOrderDetail(workOrderId) },
        onResetResult = { viewModel.resetConfirmResult() }
    )

    HandleWorkOrderActionResult(
        result = arriveResult,
        action = WorkOrderAction.Arrive,
        onSuccess = { viewModel.loadWorkOrderDetail(workOrderId) },
        onResetResult = { viewModel.resetArriveResult() }
    )

    HandleWorkOrderActionResult(
        result = rollbackResult,
        action = WorkOrderAction.Rollback,
        onSuccess = { viewModel.loadWorkOrderDetail(workOrderId) },
        onResetResult = { viewModel.resetRollbackResult() }
    )

    HandleWorkOrderActionResult(
        result = cancelResult,
        action = WorkOrderAction.Cancel,
        onSuccess = { viewModel.loadWorkOrderDetail(workOrderId) },
        onResetResult = { viewModel.resetCancelResult() }
    )

    // 工单操作对话框
    WorkOrderActionDialog(
        state = acceptDialogState,
        action = WorkOrderAction.Accept,
        isLoading = isAcceptLoading,
        onAction = { viewModel.acceptWorkOrder(it) }
    )

    WorkOrderActionDialog(
        state = dealDialogState,
        action = WorkOrderAction.Deal,
        isLoading = isDealLoading,
        onAction = { viewModel.dealWorkOrder(it) }
    )

    WorkOrderActionDialog(
        state = confirmDialogState,
        action = WorkOrderAction.Confirm,
        isLoading = isConfirmLoading,
        onAction = { viewModel.confirmWorkOrder(it) }
    )

    WorkOrderActionDialog(
        state = arriveDialogState,
        action = WorkOrderAction.Arrive,
        isLoading = isArriveLoading,
        onAction = { viewModel.arriveWorkOrder(it) }
    )

    WorkOrderActionDialog(
        state = rollbackDialogState,
        action = WorkOrderAction.Rollback,
        isLoading = isRollbackLoading,
        onAction = { viewModel.rollbackWorkOrder(it) }
    )

    WorkOrderActionDialog(
        state = cancelDialogState,
        action = WorkOrderAction.Cancel,
        isLoading = isCancelLoading,
        onAction = { viewModel.cancelWorkOrder(it) }
    )

    // 加载工单详情
    LaunchedEffect(workOrderId) {
        viewModel.loadWorkOrderDetail(workOrderId)
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("工单详情") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }

                error != null -> {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = "加载失败: ${error}",
                            color = MaterialTheme.colorScheme.error
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = { viewModel.loadWorkOrderDetail(workOrderId) }
                        ) {
                            Text("重试")
                        }
                    }
                }

                workOrderDetail != null -> {
                    WorkOrderDetailContent(
                        workOrder = workOrderDetail!!,
                        acceptDialogState = acceptDialogState,
                        dealDialogState = dealDialogState,
                        confirmDialogState = confirmDialogState,
                        arriveDialogState = arriveDialogState,
                        rollbackDialogState = rollbackDialogState,
                        cancelDialogState = cancelDialogState,
                        onAssign = {
                            selectedWorkOrderId = it
                            handleAssign(it)
                        },
                        onDeal = { viewModel.dealWorkOrder(it) },
                        onMaterial = {
                            val intent = Intent(context, PickMaterialActivity::class.java).apply {
                                putExtra(EXTRA_WORK_ORDER_ID, it)
                            }
                            launcher.launch(intent)
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun WorkOrderDetailContent(
    workOrder: WorkOrderDetailResponse,
    acceptDialogState: WorkOrderActionDialogState,
    dealDialogState: WorkOrderActionDialogState,
    confirmDialogState: WorkOrderActionDialogState,
    arriveDialogState: WorkOrderActionDialogState,
    rollbackDialogState: WorkOrderActionDialogState,
    cancelDialogState: WorkOrderActionDialogState,
    onAssign: (String) -> Unit,
    onDeal: (String) -> Unit,
    onMaterial: (String) -> Unit,
) {
    val scrollState = rememberScrollState()
    val context = LocalContext.current

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
            .padding(16.dp)
    ) {
        // 优先级和标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 优先级标签
            val priorityColor = when (workOrder.priority) {
                "高" -> Color(0xFFE53935)
                "中" -> Color(0xFFFFA000)
                "低" -> Color(0xFF43A047)
                else -> Color(0xFF43A047)
            }

            Text(
                text = workOrder.priority,
                color = priorityColor,
                fontWeight = FontWeight.Bold,
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier
                    .background(
                        color = priorityColor.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(4.dp)
                    )
                    .padding(horizontal = 8.dp, vertical = 4.dp)
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = workOrder.title,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 基本信息
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(modifier = Modifier.padding(8.dp)) {
                InfoItem(label = "工单编号", value = workOrder.number)
                Divider(modifier = Modifier.padding(vertical = 8.dp))
                InfoItem(label = "当前状态", value = workOrder.currentStatus)
                Divider(modifier = Modifier.padding(vertical = 8.dp))
                InfoItem(label = "所属车站", value = workOrder.station)
                Divider(modifier = Modifier.padding(vertical = 8.dp))
                InfoItem(label = "所属线路", value = workOrder.line)
                Divider(modifier = Modifier.padding(vertical = 8.dp))
                InfoItem(label = "工单类型", value = workOrder.type)
                Divider(modifier = Modifier.padding(vertical = 8.dp))
                InfoItem(label = "所属班组", value = workOrder.group)
            }
        }

        // 人员信息
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                // 负责人
                PersonInfoRow(
                    title = "负责人",
                    name = workOrder.manager?.name ?: "",
                    phone = workOrder.manager?.phone ?: ""
                )

                Divider(modifier = Modifier.padding(vertical = 8.dp))

                // 维修人员
                PersonInfoRow(
                    title = "维修人员",
                    name = workOrder.repairman?.name ?: "",
                    phone = workOrder.repairman?.phone ?: ""
                )
            }
        }

        // 备品备件
        if (!workOrder.standby.isNullOrEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))

            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "备品备件",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        contentPadding = PaddingValues(vertical = 4.dp)
                    ) {
                        items(workOrder.standby) { item ->
                            ItemChip(name = item.name, amount = item.amount)
                        }
                    }
                }
            }
        }

        // 工器具
        if (!workOrder.tool.isNullOrEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))

            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "工器具",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        contentPadding = PaddingValues(vertical = 4.dp)
                    ) {
                        items(workOrder.tool) { item ->
                            ItemChip(name = item.name, amount = item.amount)
                        }
                    }
                }
            }
        }

        // 工单描述
        Spacer(modifier = Modifier.height(16.dp))

        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "工单描述",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = workOrder.description ?: "",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        // 维修指导
        Spacer(modifier = Modifier.height(16.dp))

        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "维修指导",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = workOrder.suggestion ?: "",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        // 维修手册
        if (!workOrder.guides.isNullOrEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))

            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "维修手册",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    val validGuides = workOrder.guides.filterNotNull()
                    if (validGuides.isEmpty()) {
                        Text(
                            text = "暂无维修手册",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                        )
                    } else {
                        validGuides.forEachIndexed { index, guide ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clip(RoundedCornerShape(8.dp))
                                    .background(MaterialTheme.colorScheme.surface)
                                    .clickable {
                                        DownloadUtil.downloadAndOpen(context, guide.name, guide.url)
                                    }
                                    .padding(12.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Download,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.primary
                                )

                                Spacer(modifier = Modifier.width(8.dp))

                                Text(
                                    text = guide.name,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.weight(1f)
                                )
                            }

                            if (index < validGuides.size - 1) {
                                Spacer(modifier = Modifier.height(8.dp))
                            }
                        }
                    }
                }
            }
        }

        // 操作按钮
        Spacer(modifier = Modifier.height(24.dp))

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 12.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 根据工单状态显示不同的操作按钮
            when (workOrder.status) {
                "待派发" -> {
                    Button(
                        onClick = { onAssign(workOrder.id) },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("指派")
                    }
                    Button(
                        onClick = { cancelDialogState.show(workOrder.id) },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("作废")
                    }
                }

                "待接单" -> {
                    Button(
                        onClick = { rollbackDialogState.show(workOrder.id) },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("回退")
                    }
                    Button(
                        onClick = { acceptDialogState.show(workOrder.id) },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("接单")
                    }
                }

                "待领料" -> {
                    Button(
                        onClick = {
                            onMaterial(workOrder.id)
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("领料")
                    }
                }

                "待到场" -> {
                    Button(
                        onClick = { arriveDialogState.show(workOrder.id) },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("到场")
                    }
                }

                "待处理" -> {
                    Button(
                        onClick = {
                            onDeal(workOrder.id)
                            ProcessWorkOrderActivity.start(context, workOrder.id)
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("处理")
                    }
                }

                "待完成" -> {
                    Button(
                        onClick = {
                            ProcessWorkOrderActivity.start(context, workOrder.id)
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("处理")
                    }
                }

                "待确认" -> {
                    Button(
                        onClick = { confirmDialogState.show(workOrder.id) },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("确认")
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(32.dp))
    }
}

@Composable
private fun InfoItem(label: String, value: String?) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.width(80.dp)
        )

        Text(
            text = value ?: "",
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Composable
private fun PersonInfoRow(
    title: String,
    name: String,
    phone: String
) {
    val context = LocalContext.current

    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.width(80.dp)
        )

        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = null,
                modifier = Modifier.size(16.dp),
                tint = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.width(4.dp))

            Text(
                text = name,
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.width(16.dp))

            Icon(
                imageVector = Icons.Default.Phone,
                contentDescription = null,
                modifier = Modifier.size(16.dp),
                tint = MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.width(4.dp))

            Text(
                text = phone,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.clickable {
                    val intent = Intent(Intent.ACTION_DIAL).apply {
                        data = Uri.parse("tel:$phone")
                    }
                    context.startActivity(intent)
                }
            )
        }
    }
}

@Composable
private fun ItemChip(name: String, amount: Int) {
    Surface(
        shape = RoundedCornerShape(16.dp),
        color = MaterialTheme.colorScheme.surface,
        border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.5f))
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = name,
                style = MaterialTheme.typography.bodyMedium
            )

            Spacer(modifier = Modifier.width(4.dp))

            Box(
                modifier = Modifier
                    .size(24.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = amount.toString(),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Composable
private fun ActionButton(text: String, onClick: () -> Unit) {
    Button(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Text(text = text)
    }
}

@Preview(showBackground = true)
@Composable
private fun ActionButtonPreview() {
    ActionButton(text = "接单", onClick = {})
}

@Preview(showBackground = true)
@Composable
private fun ItemChipPreview() {
    ItemChip(name = "扳手", amount = 2)
}

@Preview(showBackground = true)
@Composable
private fun PersonInfoRowPreview() {
    PersonInfoRow(
        title = "负责人",
        name = "张三",
        phone = "13800138000"
    )
} 
