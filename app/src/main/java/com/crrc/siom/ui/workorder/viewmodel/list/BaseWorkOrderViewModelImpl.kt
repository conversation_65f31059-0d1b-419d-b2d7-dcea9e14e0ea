package com.crrc.siom.ui.workorder.viewmodel.list

import android.util.Log
import androidx.lifecycle.ViewModel
import com.crrc.common.bean.response.MembersResponse
import com.crrc.siom.data.repository.WorkOrderRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

abstract class BaseWorkOrderViewModelImpl<T> : ViewModel(), BaseWorkOrderViewModel<T> {
    protected val _workOrders = MutableStateFlow<List<T>>(emptyList())
    override val workOrders: StateFlow<List<T>> = _workOrders.asStateFlow()

    protected val _isLoading = MutableStateFlow(false)
    override val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    protected val _isLoadingMore = MutableStateFlow(false)
    override val isLoadingMore: StateFlow<Boolean> = _isLoadingMore.asStateFlow()

    protected val _hasMoreData = MutableStateFlow(true)
    override val hasMoreData: StateFlow<Boolean> = _hasMoreData.asStateFlow()

    protected val _error = MutableStateFlow<String?>(null)
    override val error: StateFlow<String?> = _error.asStateFlow()

    protected val _currentPage = MutableStateFlow(1)
    override val currentPage: StateFlow<Int> = _currentPage.asStateFlow()

    protected val pageSize = 10

    protected var isProcessingLoadMore = false

    // 回退加载状态
    protected val _isRollbackLoading = MutableStateFlow(false)
    val isRollbackLoading: StateFlow<Boolean> = _isRollbackLoading.asStateFlow()
    protected val _rollbackResult = MutableStateFlow<Pair<Boolean, String?>?>(null)
    val rollbackResult: StateFlow<Pair<Boolean, String?>?> = _rollbackResult.asStateFlow()

    // 作废加载状态
    protected val _isCancelLoading = MutableStateFlow(false)
    val isCancelLoading: StateFlow<Boolean> = _isCancelLoading.asStateFlow()
    protected val _cancelResult = MutableStateFlow<Pair<Boolean, String?>?>(null)
    val cancelResult: StateFlow<Pair<Boolean, String?>?> = _cancelResult.asStateFlow()

    // 接单加载状态
    protected val _isAcceptLoading = MutableStateFlow(false)
    val isAcceptLoading: StateFlow<Boolean> = _isAcceptLoading.asStateFlow()
    protected val _acceptResult = MutableStateFlow<Pair<Boolean, String?>?>(null)
    val acceptResult: StateFlow<Pair<Boolean, String?>?> = _acceptResult.asStateFlow()

    // 处理加载状态
    protected val _isDealLoading = MutableStateFlow(false)
    val isDealLoading: StateFlow<Boolean> = _isDealLoading.asStateFlow()
    protected val _dealResult = MutableStateFlow<Pair<Boolean, String?>?>(null)
    val dealResult: StateFlow<Pair<Boolean, String?>?> = _dealResult.asStateFlow()

    //确认加载状态
    protected val _isConfirmLoading = MutableStateFlow(false)
    val isConfirmLoading: StateFlow<Boolean> = _isConfirmLoading.asStateFlow()
    protected val _confirmResult = MutableStateFlow<Pair<Boolean, String?>?>(null)
    val confirmResult: StateFlow<Pair<Boolean, String?>?> = _confirmResult.asStateFlow()

    //到场加载状态
    protected val _isArriveLoading = MutableStateFlow(false)
    val isArriveLoading: StateFlow<Boolean> = _isArriveLoading.asStateFlow()
    protected val _arriveResult = MutableStateFlow<Pair<Boolean, String?>?>(null)
    val arriveResult: StateFlow<Pair<Boolean, String?>?> = _arriveResult.asStateFlow()

    protected val _filterParams = MutableStateFlow<Map<String, Any>>(emptyMap())

    override fun refresh() {
        println("BaseWorkOrderViewModel: refresh() 被调用")
        println("调用栈: ${Thread.currentThread().stackTrace.take(10).joinToString("\n")}")
        _currentPage.value = 1
        _isLoading.value = true
        _error.value = null
        _workOrders.value = emptyList()
        loadData(true)
    }

    override fun loadMore() {
        if (!_isLoadingMore.value && _hasMoreData.value && !isProcessingLoadMore) {
            isProcessingLoadMore = true
            _isLoadingMore.value = true

            loadData(false)
        }
    }

    protected abstract fun loadData(isRefresh: Boolean)

    /**
     * 回退
     */
    fun rollbackWorkOrder(id: String) {
        _isRollbackLoading.value = true

        getWorkOrderRepository().rollbackWorkOrder(id) { success, error ->
            _isRollbackLoading.value = false
            _rollbackResult.value = Pair(success, error)
        }
    }

    fun resetRollbackResult() {
        _rollbackResult.value = null
    }

    /**
     * 作废
     */
    fun cancelWorkOrder(id: String) {
        _isCancelLoading.value = true

        getWorkOrderRepository().cancelWorkOrder(id) { success, error ->
            _isCancelLoading.value = false
            _cancelResult.value = Pair(success, error)
        }
    }

    fun resetCancelResult() {
        _cancelResult.value = null
    }

    /**
     * 接单
     */
    fun acceptWorkOrder(id: String){
        _isAcceptLoading.value = true

        getWorkOrderRepository().acceptWorkOrder(id) {success, error ->
            _isAcceptLoading.value = false
            _cancelResult.value = Pair(success, error)
        }
    }

    fun resetAcceptResult(){
        _acceptResult.value = null
    }

    /**
     * 处理
     */
    fun dealWorkOrder(id: String){
        _isDealLoading.value = true

        getWorkOrderRepository().dealWorkOrder(id) {success, error ->
            _isDealLoading.value = false
            _dealResult.value = Pair(success, error)
        }
    }

    fun resetDealResult(){
        _dealResult.value = null
    }

    /**
     * 确认
     */
    fun confirmWorkOrder(id: String){
        _isConfirmLoading.value = true

        getWorkOrderRepository().confirmWorkOrder(id) {success, error ->
            _isConfirmLoading.value = false
            _confirmResult.value = Pair(success, error)
        }
    }

    fun resetConfirmResult(){
        _confirmResult.value = null
    }
    /**
     * 到场
     */
    fun arriveWorkOrder(id: String){
        _isArriveLoading.value = true

        getWorkOrderRepository().arriveWorkOrder(id) {success, error ->
            _isArriveLoading.value = false
            _arriveResult.value = Pair(success, error)
        }
    }

    fun resetArriveResult(){
        _arriveResult.value = null
    }


    fun setFilterParams(params: Map<String, Any>) {
        Log.d("WorkOrder", "BaseWorkOrderViewModel: setFilterParams 被调用")
        Log.d("WorkOrder", "调用栈: ${Thread.currentThread().stackTrace.take(10).joinToString("\n")}")
        _filterParams.value = params
        refresh()
    }

    // 获取可指派人员
    fun assignOpt(workOrderId: String, callback: (MembersResponse?, String?) -> Unit) {
        getWorkOrderRepository().assignOpt(workOrderId) { members, error ->
            callback(members, error)
        }
    }

    // 提交指派人员
    fun assign(workOrderId: String, members: List<MembersResponse.FilterParam>, callback: (Boolean, String?) -> Unit) {
        getWorkOrderRepository().assign(workOrderId, members) { success, msg ->
            callback(success, msg)
        }
    }


    protected abstract fun getWorkOrderRepository(): WorkOrderRepository
}